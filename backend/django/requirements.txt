# This file was autogenerated by uv via the following command:
#    uv pip compile requirements.in --unsafe-package setuptools -o requirements.txt
asgiref==3.8.1
    # via django
django==5.2.2
    # via
    #   -r requirements.in
    #   django-extensions
django-extensions==4.1
    # via -r requirements.in
factory-boy==3.3.3
    # via -r requirements.in
faker==37.3.0
    # via factory-boy
iniconfig==2.1.0
    # via pytest
packaging==25.0
    # via pytest
pluggy==1.6.0
    # via pytest
psycopg2-binary==2.9.10
    # via -r requirements.in
pygments==2.19.1
    # via pytest
pytest==8.4.0
    # via pytest-django
pytest-django==4.11.1
    # via -r requirements.in
sqlparse==0.5.3
    # via django
tzdata==2025.2
    # via faker
