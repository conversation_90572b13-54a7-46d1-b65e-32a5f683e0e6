from .base import *  # noqa: F403, F401

# Development-specific settings

DEBUG = True

# Database for development
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "backtest",
        "USER": "backtest",
        "PASSWORD": "backtest",
        "HOST": "backtest_db",
        "PORT": "5432",
    }
}

# Allow all hosts in development
ALLOWED_HOSTS = ["*"]
