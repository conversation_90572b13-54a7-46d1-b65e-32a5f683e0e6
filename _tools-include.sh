#!/usr/bin/env bash

# Shared functions for dev-tools scripts

print_help() {
    echo "Usage: $0 <command>"
    echo ""
    echo "Available commands:"
    for cmd in "${AVAILABLE_COMMANDS[@]}"; do
        printf "  %-20s %s\n" $(echo "$cmd" | cut -d' ' -f1) "$(echo "$cmd" | cut -d' ' -f2-)"
    done
}

install_autocompletion() {
    local commands=()
    for cmd in "${AVAILABLE_COMMANDS[@]}"; do
        commands+=($(echo "$cmd" | cut -d' ' -f1))
    done
    
    complete -W "${commands[*]}" "$SCRIPT_NAME"
    complete -W "${commands[*]}" "./$SCRIPT_NAME.sh"
}
