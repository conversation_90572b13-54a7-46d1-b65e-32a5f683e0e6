services:

  backtest_db:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: backtest
      POSTGRES_PASSWORD: backtest
      POSTGRES_DB: backtest
    expose:
      - 5432
    volumes:
      - ../shared:/shared
      - backtest_postgres_data:/var/lib/postgresql/data/

  backtest_backend:
    container_name: backtest_backend
    build:
      context: ../..
      dockerfile: docker/dev/backtest_backend.Dockerfile
    image: backtest_backend
    stdin_open: true
    tty: true
    volumes:
      - ../..:/usr/src/backtest/
      - ipython_history:/root/.ipython
      - backtest_media:/usr/src/backtest/backend/django/media/
      - backtest_venv:/opt/venv
    ports:
      - 3002:8000
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      - backtest_db
    command: >
      sh -c 'python -Wa manage.py runserver 0.0.0.0:8000;'

  backtest_ws:
    build:
      context: ../..
      dockerfile: docker/dev/backtest_ws.Dockerfile
    ports:
      - 80:8080
    depends_on:
      - backtest_backend

volumes:
  ipython_history:
  backtest_media:
  backtest_postgres_data:
  backtest_venv:
